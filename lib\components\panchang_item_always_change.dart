import 'package:flutter/material.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils/countdown_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:panchang_at_this_moment/components/panchang_info_card.dart';
import 'package:panchang_at_this_moment/components/panchang_nakshatra_card.dart';

// Simplified data structure for component configuration
class PanchangComponentConfig {
  final String titleKey;
  final Color color;
  final IconData icon;
  final PanchangAlwaysChangeItem? timeBasedItem;
  final String? staticValue;
  final List<Map<String, String>>? infoRows;
  final List<PanchangAlwaysChangeItem>? timeBasedItems; // For multi-info cards with timer

  PanchangComponentConfig({
    required this.titleKey,
    required this.color,
    required this.icon,
    this.timeBasedItem,
    this.staticValue,
    this.infoRows,
    this.timeBasedItems,
  });
}

// Helper function to build a component widget
Widget _buildPanchangComponent(
  PanchangComponentConfig config,
  BuildContext context,
  DateTime currentTime,
) {
  final title = _getLocalizedTitle(config.titleKey, context);

  if (config.timeBasedItem != null) {
    // Time-based component with countdown
    return CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: config.timeBasedItem!.name,
          startTime: config.timeBasedItem!.start,
          endTime: config.timeBasedItem!.end,
        )
      ],
      child: PanchangInfoCard(
        color: config.color,
        icon: config.icon,
        title: title,
        value: config.timeBasedItem!.name,
        context: context,
      ),
    );
  } else if (config.infoRows != null) {
    // Multi-info component (like Nakshatra card) with countdown functionality
    final nakshatraCard = PanchangNakshatraCard(
      color: config.color,
      icon: config.icon,
      title: title,
      infoRows: config.infoRows!,
      infoBgColor: config.color.withValues(alpha: 0.05),
    );

    // Add countdown functionality if timeBasedItems are provided
    if (config.timeBasedItems != null && config.timeBasedItems!.isNotEmpty) {
      return CountDownWithTimeDialog(
        currentTime: currentTime,
        countDownItems: config.timeBasedItems!.map((item) => CountDownItem(
          title: item.name,
          startTime: item.start,
          endTime: item.end,
        )).toList(),
        child: nakshatraCard,
      );
    }

    return nakshatraCard;
  } else {
    // Simple static component
    return PanchangInfoCard(
      color: config.color,
      icon: config.icon,
      title: title,
      value: config.staticValue ?? '',
      context: context,
    );
  }
}

String _getLocalizedTitle(String titleKey, BuildContext context) {
  final localizations = AppLocalizations.of(context)!;
  switch (titleKey) {
    case 'tithiPaksha': return localizations.tithiPaksha;
    case 'karana': return localizations.karana;
    case 'yoga': return localizations.yoga;
    case 'maasa': return localizations.maasa;
    case 'samvatsara': return localizations.samvatsara;
    case 'nakshatra': return localizations.nakshatra;
    case 'suryaNakshatra': return localizations.suryaNakshatra;
    default: return titleKey;
  }
}

List<Widget> getPanchangItemAlwayChange({
  required PanchangDataForThreeDays? panchangData,
  required DateTime currentTime,
  required BuildContext context
}) {
  // Helper function to get current item from time-based lists
  PanchangAlwaysChangeItem getCurrentItem(List<List<PanchangAlwaysChangeItem>> dayLists) {
    final allItems = dayLists.expand((x) => x).toList();
    return _getCurrentNakshatra(currentTime, allItems);
  }

  // Get all current time-based items
  final currentTithiPaksha = getCurrentItem([
    panchangData?.previousDay.tithiPakshaList ?? [],
    panchangData?.currentDay.tithiPakshaList ?? [],
    panchangData?.nextDay.tithiPakshaList ?? []
  ]);

  final currentKarana = getCurrentItem([
    panchangData?.previousDay.karanaList ?? [],
    panchangData?.currentDay.karanaList ?? [],
    panchangData?.nextDay.karanaList ?? []
  ]);

  final currentYoga = getCurrentItem([
    panchangData?.previousDay.yogaList ?? [],
    panchangData?.currentDay.yogaList ?? [],
    panchangData?.nextDay.yogaList ?? []
  ]);

  final currentNakshatra = getCurrentItem([
    panchangData?.previousDay.nakshatraList ?? [],
    panchangData?.currentDay.nakshatraList ?? [],
    panchangData?.nextDay.nakshatraList ?? []
  ]);

  final currentNakshatraPada = getCurrentItem([
    panchangData?.previousDay.nakshatraPadaList ?? [],
    panchangData?.currentDay.nakshatraPadaList ?? [],
    panchangData?.nextDay.nakshatraPadaList ?? []
  ]);

  final currentLunarRaasi = getCurrentItem([
    panchangData?.previousDay.lunarRaasiList ?? [],
    panchangData?.currentDay.lunarRaasiList ?? [],
    panchangData?.nextDay.lunarRaasiList ?? []
  ]);

  final currentSuryaNakshatra = getCurrentItem([
    panchangData?.previousDay.suryaNakshatraList ?? [],
    panchangData?.currentDay.suryaNakshatraList ?? [],
    panchangData?.nextDay.suryaNakshatraList ?? []
  ]);

  final currentSuryaPada = getCurrentItem([
    panchangData?.previousDay.suryaPadaList ?? [],
    panchangData?.currentDay.suryaPadaList ?? [],
    panchangData?.nextDay.suryaPadaList ?? []
  ]);

  final currentSolarRaasi = getCurrentItem([
    panchangData?.previousDay.solarRaasiList ?? [],
    panchangData?.currentDay.solarRaasiList ?? [],
    panchangData?.nextDay.solarRaasiList ?? []
  ]);

  // Static values
  final maasa = panchangData?.currentDay.maasa ?? '';
  final samvatsara = panchangData?.currentDay.samvatsara ?? '';

  // Define component configurations
  final configs = [
    // Tithi Paksha Card
    PanchangComponentConfig(
      titleKey: 'tithiPaksha',
      color: Colors.orange,
      icon: Icons.calendar_today,
      timeBasedItem: currentTithiPaksha,
    ),

    // Karana Card
    PanchangComponentConfig(
      titleKey: 'karana',
      color: Colors.indigo,
      icon: Icons.access_time,
      timeBasedItem: currentKarana,
    ),

    // Yoga Card
    PanchangComponentConfig(
      titleKey: 'yoga',
      color: Colors.teal,
      icon: Icons.autorenew,
      timeBasedItem: currentYoga,
    ),

    // Maasa Card
    PanchangComponentConfig(
      titleKey: 'maasa',
      color: Colors.amber,
      icon: Icons.date_range,
      staticValue: maasa,
    ),

    // Samvatsara Card
    PanchangComponentConfig(
      titleKey: 'samvatsara',
      color: Colors.purple,
      icon: Icons.event,
      staticValue: samvatsara,
    ),

    // Nakshatra Card (complex multi-info card)
    PanchangComponentConfig(
      titleKey: 'nakshatra',
      color: Colors.blueGrey,
      icon: Icons.nightlight_round,
      infoRows: [
        {
          'label': AppLocalizations.of(context)!.nakshatra,
          'value': currentNakshatra.name,
        },
        {
          'label': AppLocalizations.of(context)!.nakshatraPada,
          'value': currentNakshatraPada.name,
        },
        {
          'label': AppLocalizations.of(context)!.rashi,
          'value': currentLunarRaasi.name,
        },
      ],
      timeBasedItems: [currentNakshatra, currentNakshatraPada, currentLunarRaasi],
    ),

    // Surya Nakshatra Card (complex multi-info card)
    PanchangComponentConfig(
      titleKey: 'suryaNakshatra',
      color: Colors.orange,
      icon: Icons.wb_sunny,
      infoRows: [
        {
          'label': AppLocalizations.of(context)!.suryaNakshatra,
          'value': currentSuryaNakshatra.name,
        },
        {
          'label': AppLocalizations.of(context)!.suryaNakshatraPada,
          'value': currentSuryaPada.name,
        },
        {
          'label': AppLocalizations.of(context)!.suryaRashi,
          'value': currentSolarRaasi.name,
        },
      ],
      timeBasedItems: [currentSuryaNakshatra, currentSuryaPada, currentSolarRaasi],
    ),
  ];

  // Build and return widgets using the simplified helper
  return configs.map((config) => _buildPanchangComponent(config, context, currentTime)).toList();
}


PanchangAlwaysChangeItem _getCurrentNakshatra(
    DateTime currentTime, List<PanchangAlwaysChangeItem> nakshatraList) {
  return nakshatraList.firstWhere(
      (element) =>
          element.start.isBefore(currentTime) &&
          element.end.isAfter(currentTime),
      orElse: () => PanchangAlwaysChangeItem(
            name: 'Failed to get data',
            start: DateTime.now(),
            end: DateTime.now(),
          ));
}